# USPS运单标签生成器

基于您的Temu API代码，实现生成USPS风格运单标签的工具。

## 功能特性

✅ **API数据获取**: 从Temu平台获取包裹物流信息  
✅ **USPS标签生成**: 生成符合USPS标准的运单标签图片  
✅ **高质量输出**: 4x6英寸，300 DPI，适合打印  
✅ **完整元素**: 包含发件人、收件人、追踪号、条形码、二维码等  
✅ **PDF支持**: 可选生成PDF格式标签  

## 安装依赖

### 方法1: 自动安装
```bash
python install_dependencies.py
```

### 方法2: 手动安装
```bash
pip install requests Pillow qrcode[pil]
# 可选 (用于PDF生成)
pip install reportlab
```

## 使用方法

### 简化版本 (推荐)
```bash
python simple_label_generator.py
```

### 完整版本
```bash
python test.py
```

## 输出文件

生成的文件保存在 `shipping_labels/` 目录下:

- `usps_label_[追踪号].png` - USPS风格运单标签图片
- `label_[追踪号].pdf` - PDF格式标签 (完整版本)
- `original_[追踪号].pdf` - 原始PDF (如果API支持)

## 标签内容

生成的标签包含以下信息:
- **USPS标题和Logo**
- **发件人信息**: 姓名、地址、城市州邮编
- **收件人信息**: 姓名、地址、城市州邮编  
- **追踪号码**: 带条形码和文本
- **二维码**: 用于快速扫描
- **邮资标记**: "U.S. POSTAGE PAID"
- **服务类型**: "USPS GROUND ADVANTAGE™"

## 自定义配置

### 修改包裹信息
编辑代码中的 `sample_package` 变量:

```python
sample_package = {
    'tracking_number': '您的追踪号',
    'sender': {
        'name': '发件人姓名',
        'address': '发件人地址',
        'city_state_zip': '城市 州 邮编'
    },
    'recipient': {
        'name': '收件人姓名', 
        'address': '收件人地址',
        'city_state_zip': '城市 州 邮编'
    }
}
```

### 修改API配置
更新 `headers` 和 `cookies` 中的认证信息:
- `mallid`: 您的商户ID
- `seller_temp`: 您的认证token
- 其他必要的认证头信息

## API集成

### 获取包裹数据
```python
generator = SimpleShippingLabelGenerator()
package_data = generator.get_package_data()
```

### 生成标签
```python
img = generator.generate_usps_label_image(package_info)
img.save("label.png", 'PNG', dpi=(300, 300))
```

## 故障排除

### 常见问题

1. **API请求失败**
   - 检查网络连接
   - 更新cookies中的seller_temp token
   - 确认mallid正确

2. **字体加载失败**
   - 程序会自动使用默认字体
   - Windows系统通常有arial.ttf字体

3. **依赖安装失败**
   - 使用管理员权限运行
   - 尝试使用conda安装: `conda install pillow`

### 错误代码

- **状态码403**: 认证失败，需要更新token
- **状态码404**: API端点不存在
- **状态码500**: 服务器内部错误

## 技术细节

### 图片规格
- **尺寸**: 4x6英寸 (1200x1800像素)
- **分辨率**: 300 DPI
- **格式**: PNG
- **颜色**: RGB

### 条形码
- **类型**: 简化版条形码 (可升级为Code128)
- **数据**: 追踪号码
- **位置**: 标签中下部

### 二维码
- **库**: qrcode
- **数据**: 追踪号码和USPS标识
- **位置**: 左下角和右下角

## 扩展功能

### 批量处理
可以修改代码以支持批量生成多个包裹的标签:

```python
def batch_generate_labels(packages_list):
    for package in packages_list:
        img = generate_usps_label_image(package)
        filename = f"label_{package['tracking_number']}.png"
        img.save(filename)
```

### 自定义模板
可以创建不同的标签模板:
- FedEx风格
- UPS风格  
- DHL风格

## 许可证

本工具仅供学习和个人使用。请遵守相关API的使用条款。

## 支持

如有问题，请检查:
1. Python版本 (建议3.7+)
2. 依赖包版本
3. API认证信息
4. 网络连接状态
