#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Temu店铺待发货订单导出工具
功能：
1. 自动获取店铺所有待发货订单
2. 导出为Excel和CSV格式
3. 包含详细的订单信息和状态
"""

import requests
import json
import csv
import pandas as pd
from datetime import datetime
import os


class TemuOrderExporter:
    def __init__(self, mall_id, access_token, cookies_dict):
        """
        初始化导出工具
        
        Args:
            mall_id: 店铺ID
            access_token: 访问令牌
            cookies_dict: 完整的cookies字典
        """
        self.mall_id = mall_id
        self.access_token = access_token
        self.cookies = cookies_dict
        self.headers = {
            "accept": "application/json, text/plain, */*",
            "accept-language": "zh-CN,zh;q=0.9",
            "content-type": "application/json;charset=UTF-8",
            "mallid": str(mall_id),  # 确保mallid是字符串类型
            "origin": "https://agentseller-us.temu.com",
            "priority": "u=1, i",
            "referer": "https://agentseller-us.temu.com/mmsos/orders.html",
            "sec-ch-ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "\"Windows\"",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
            "x-document-referer": "https://agentseller-us.temu.com/mmsos/orders.html",
            "x-phan-data": "0aeJx7xMxiYPiIOSza0NzU0NTI0sTE0sjSXAfGMzU1NzFD8MxMLM1iAUBwC6Q"
        }
        self.url = "https://agentseller-us.temu.com/kirogi/bg/mms/recentOrderList"

    def get_pending_orders(self, max_pages=10):
        """
        获取待发货订单列表
        
        Args:
            max_pages: 最大页数限制，防止无限循环
            
        Returns:
            list: 待发货订单列表
        """
        print("正在获取待发货订单...")
        all_pending_orders = []
        page_number = 1
        
        while page_number <= max_pages:
            print(f"正在获取第 {page_number} 页...")
            
            data = {
                "fulfillmentMode": 0,
                "pageNumber": page_number,
                "pageSize": 500,
                "queryType": 2,  # 查询类型，2可能表示待发货
                "sortType": 1,
                "timeZone": "UTC+8",
                "parentAfterSalesTag": 0,
                "needBuySignService": 0,
                "sellerNoteLabelList": []
            }
            
            try:
                data_json = json.dumps(data, separators=(',', ':'))
                response = requests.post(self.url, headers=self.headers, cookies=self.cookies, data=data_json)
                
                if response.status_code != 200:
                    print(f"请求失败，状态码: {response.status_code}")
                    break
                    
                result = response.json()
                
                if not result.get('success'):
                    print(f"API返回错误: {result.get('errorMsg')}")
                    break
                    
                page_items = result.get('result', {}).get('pageItems', [])
                
                if not page_items:
                    print("没有更多订单数据")
                    break
                
                # 筛选待发货订单
                page_pending_count = 0
                for item in page_items:
                    parent_order = item.get('parentOrderMap', {})
                    order_list = item.get('orderList', [])
                    
                    # 检查是否为待发货状态
                    if (parent_order.get('parentPackageStatus') == 1 and 
                        parent_order.get('parentOrderStatus') == 2):
                        
                        for order in order_list:
                            # 进一步检查订单级别的状态
                            if (order.get('orderPackageStatus') == 1 and 
                                order.get('shippedQuantity', 0) == 0 and 
                                order.get('unShippedQuantity', 0) > 0):
                                
                                all_pending_orders.append({
                                    'parent_order': parent_order,
                                    'order': order
                                })
                                page_pending_count += 1
                
                print(f"第 {page_number} 页找到 {page_pending_count} 个待发货订单")
                
                # 检查是否还有更多页面
                if len(page_items) < 500:
                    print("已获取所有页面")
                    break
                    
                page_number += 1
                
            except Exception as e:
                print(f"获取第 {page_number} 页时发生错误: {str(e)}")
                break
        
        print(f"总共找到 {len(all_pending_orders)} 个待发货订单")
        return all_pending_orders

    def extract_order_info(self, pending_orders):
        """
        提取订单信息为可导出的格式
        
        Args:
            pending_orders: 待发货订单列表
            
        Returns:
            list: 格式化的订单数据
        """
        print("正在处理订单数据...")
        extracted_data = []
        
        for i, item in enumerate(pending_orders, 1):
            if i % 10 == 0:
                print(f"已处理 {i}/{len(pending_orders)} 个订单")
                
            parent_order = item['parent_order']
            order = item['order']
            
            # 提取包裹信息
            package_info = order.get('orderPackageInfoList', [{}])[0] if order.get('orderPackageInfoList') else {}
            
            # 提取地址信息
            address_info = {
                'country': parent_order.get('regionName1', ''),
                'state': parent_order.get('maskedRegionName2', ''),
                'city': parent_order.get('maskedRegionName3', ''),
            }
            
            # 计算剩余发货时间
            expect_ship_time = parent_order.get('expectShipLatestTimeStr', '')
            remaining_time = self._calculate_remaining_time(expect_ship_time)
            
            order_data = {
                # 基本订单信息
                '父订单号': parent_order.get('parentOrderSn', ''),
                '子订单号': order.get('orderSn', ''),
                '订单时间': parent_order.get('parentOrderTimeStr', ''),
                '确认时间': parent_order.get('parentConfirmTimeStr', ''),
                '最晚发货时间': expect_ship_time,
                '剩余发货时间': remaining_time,
                '预计送达时间': parent_order.get('expectDeliveryEndTimeStr', ''),
                
                # 商品信息
                '商品名称': order.get('goodsName', ''),
                '商品规格': order.get('spec', ''),
                'SKU ID': order.get('skuId', ''),
                '商品ID': order.get('goodsId', ''),
                '数量': order.get('quantity', 0),
                '未发货数量': order.get('unShippedQuantity', 0),
                '已发货数量': order.get('shippedQuantity', 0),
                
                # 包裹信息
                '包裹号': package_info.get('packageSn', ''),
                '快递单号': package_info.get('trackingNumber', ''),
                '快递公司': package_info.get('companyName', ''),
                '发货仓库': package_info.get('sendWarehouseName', ''),
                
                # 地址信息
                '收货国家': address_info['country'],
                '收货州/省': address_info['state'],
                '收货城市': address_info['city'],
                '站点': parent_order.get('siteName', ''),
                
                # 状态信息
                '父订单状态': parent_order.get('parentOrderStatus', ''),
                '订单状态': order.get('orderStatus', ''),
                '包裹状态': parent_order.get('parentPackageStatus', ''),
                '订单包裹状态': order.get('orderPackageStatus', ''),
                
                # 其他信息
                '仓库名称': order.get('warehouseName', ''),
                '是否COD订单': '是' if order.get('isCodOrder', False) else '否',
                '商品缩略图': order.get('thumbUrl', ''),
                
                # 产品信息
                '产品SKU列表': ', '.join(order.get('productSkuIdList', [])),
                '扩展代码': ', '.join(order.get('extCodeList', [])),
            }
            
            extracted_data.append(order_data)
        
        print(f"订单数据处理完成，共 {len(extracted_data)} 条记录")
        return extracted_data

    def _calculate_remaining_time(self, expect_ship_time_str):
        """计算剩余发货时间"""
        if not expect_ship_time_str:
            return "未知"
        
        try:
            expect_time = datetime.strptime(expect_ship_time_str, "%Y-%m-%d %H:%M:%S")
            now = datetime.now()
            remaining = expect_time - now
            
            if remaining.total_seconds() <= 0:
                return "已超时"
            
            days = remaining.days
            hours = remaining.seconds // 3600
            
            if days > 0:
                return f"{days}天{hours}小时"
            else:
                return f"{hours}小时"
        except:
            return "计算失败"

    def export_to_excel(self, data, filename=None):
        """导出数据到Excel文件"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"待发货订单_{timestamp}.xlsx"
        
        if not data:
            print("没有待发货订单数据可导出")
            return None
        
        try:
            df = pd.DataFrame(data)
            
            # 创建Excel写入器
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='待发货订单', index=False)
                
                # 获取工作表
                worksheet = writer.sheets['待发货订单']
                
                # 自动调整列宽
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)  # 最大宽度50
                    worksheet.column_dimensions[column_letter].width = adjusted_width
            
            print(f"已导出 {len(data)} 条待发货订单到Excel文件: {filename}")
            return filename
            
        except Exception as e:
            print(f"导出Excel文件时发生错误: {str(e)}")
            return None

    def export_to_csv(self, data, filename=None):
        """导出数据到CSV文件"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"待发货订单_{timestamp}.csv"
        
        if not data:
            print("没有待发货订单数据可导出")
            return None
        
        try:
            df = pd.DataFrame(data)
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            print(f"已导出 {len(data)} 条待发货订单到CSV文件: {filename}")
            return filename
            
        except Exception as e:
            print(f"导出CSV文件时发生错误: {str(e)}")
            return None

    def export_orders(self, export_excel=True, export_csv=True, max_pages=10):
        """
        完整的订单导出流程
        
        Args:
            export_excel: 是否导出Excel文件
            export_csv: 是否导出CSV文件
            max_pages: 最大页数限制
            
        Returns:
            dict: 导出结果
        """
        try:
            # 获取待发货订单
            pending_orders = self.get_pending_orders(max_pages)
            
            if not pending_orders:
                print("没有找到待发货订单")
                return {'success': False, 'message': '没有找到待发货订单'}
            
            # 提取订单信息
            order_data = self.extract_order_info(pending_orders)
            
            # 显示统计信息
            self._show_statistics(order_data)
            
            # 导出文件
            result = {
                'success': True,
                'order_count': len(order_data),
                'files': [],
                'orders': order_data  # 添加订单数据用于合并
            }

            if export_excel:
                excel_file = self.export_to_excel(order_data)
                if excel_file:
                    result['files'].append(excel_file)

            if export_csv:
                csv_file = self.export_to_csv(order_data)
                if csv_file:
                    result['files'].append(csv_file)

            return result
            
        except Exception as e:
            error_msg = f"导出过程中发生错误: {str(e)}"
            print(error_msg)
            return {'success': False, 'message': error_msg}

    def _show_statistics(self, order_data):
        """显示订单统计信息"""
        if not order_data:
            return
        
        print(f"\n=== 订单统计信息 ===")
        print(f"总订单数: {len(order_data)}")
        
        # 按站点统计
        site_stats = {}
        for order in order_data:
            site = order.get('站点', '未知')
            site_stats[site] = site_stats.get(site, 0) + 1
        
        print("按站点分布:")
        for site, count in site_stats.items():
            print(f"  {site}: {count} 个订单")
        
        # 按快递公司统计
        courier_stats = {}
        for order in order_data:
            courier = order.get('快递公司', '未知')
            if courier:
                courier_stats[courier] = courier_stats.get(courier, 0) + 1
        
        print("按快递公司分布:")
        for courier, count in courier_stats.items():
            print(f"  {courier}: {count} 个订单")
        
        # 紧急订单统计
        urgent_orders = [order for order in order_data if '小时' in order.get('剩余发货时间', '') or order.get('剩余发货时间', '') == '已超时']
        if urgent_orders:
            print(f"\n⚠️  紧急订单: {len(urgent_orders)} 个（需要在24小时内发货或已超时）")
        
        print("=" * 30)


def main():
    """主函数 - 使用示例"""
    # 配置信息（需要根据实际情况修改）
    MALL_ID = "634418218259845"
    ACCESS_TOKEN = "J73OXMYWK2RLZ5MUWHZGAOF4J6VLXJ6PMAPBNX6VCIJBZBQBT7MA011025f145cf"
    
    # 完整的cookies（需要根据实际情况修改）
    COOKIES = {
        "api_uid": "CmwYKWgQb2yulABcDpOgAg==",
        "_bee": "jmWVoOOs6u8MTHaJej00jbAskNLUYanv",
        "njrpl": "jmWVoOOs6u8MTHaJej00jbAskNLUYanv",
        "dilx": "_EsYzO7-cUf1dj6XUNIzM",
        "hfsc": "L3yOfYAw7jv/0pXLfA==",
        "_nano_fp": "XpmYn5m8n5mbXpEJl9_7bNdmhzyXs6pb4Rmyvp~T",
        "AccessToken": ACCESS_TOKEN,
        "user_uin": "BBUSAO4SDG2TDHE743EOIUKYYGS4QSYPO2PKQSWC",
        "isLogin": "1747965800295",
        "timezone": "Asia%2FShanghai",
        "region": "0",
        "webp": "1",
        "seller_temp": "N_eyJ0IjoiV2Vrdi9aQTVRelFWRWFCNHhvN3JQT09zTFlTSzdqbTlmOXBhYlM5WXNDK2lhRXZHU3ZFTi8xaXVoUjNKbldvMWtVWlRzVkh6U2E1KzRyQjNGYTR1RVE9PSIsInYiOjEsInMiOjEwMDAxLCJ1IjoyMzUxNDQ1MzE4NDUyMX0=",
        "mallid": str(MALL_ID)  # 确保mallid是字符串类型
    }
    
    print("=== Temu店铺待发货订单导出工具 ===")
    print(f"店铺ID: {MALL_ID}")
    print("开始导出待发货订单...\n")
    
    # 创建导出器
    exporter = TemuOrderExporter(MALL_ID, ACCESS_TOKEN, COOKIES)
    
    # 执行导出
    result = exporter.export_orders(export_excel=True, export_csv=True, max_pages=20)
    
    # 显示结果
    if result['success']:
        print(f"\n✅ 导出成功!")
        print(f"共导出 {result['order_count']} 个待发货订单")
        print("生成的文件:")
        for file in result['files']:
            print(f"  📄 {file}")
    else:
        print(f"\n❌ 导出失败: {result['message']}")


if __name__ == "__main__":
    main()
